'use client';

import { useHookstate } from '@hookstate/core';
import { parseAsString, useQueryState } from 'nuqs';

import { Button } from '@/components/ui/button';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';

import { getUserType } from '@/lib/constants';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';
import BasicProfile from './basic-profile';
import CareerAcademic from './career-academic';
import { DirtyDialog } from './dirty-dialog';
import FamilyProfile from './family-profile';
import FarmDetails from './farm-details';
import FarmInsurance from './farm-insurance';
import IdentificationDocs from './identification-docs';
import LoanRequirements from './loan-requirements';
import PropertyOwnership from './property-ownership';
import { SaveConfirmation } from './save-confirmation';

export default function AccountProfileTabContent() {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  // Use nuqs for URL state management
  const [profileTab, setProfileTab] = useQueryState('tab', parseAsString.withDefault('loan_requirements'));

  const cached = useHookstate({
    profileTab: '',
    activeStep: 0,
  });
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);
  const confirmDialog = useHookstate(false);
  const dirtyDialog = useHookstate(false);
  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';

  // Find the current active step based on the tab value
  const activeStep = PROFILE_TAB.findIndex((tab) => tab.value === profileTab);

  return (
    <div className="mt-8 grid gap-6">
      {isAdmin && profileTab !== 'landbank_reqts' && (
        <div className="flex items-center justify-end gap-4">
          {!gState.accountProfileIsEdit.value && (
            <Button
              type="button"
              size="lg"
              onClick={() => {
                gState.accountProfileIsEdit.set(true);
              }}
              className="w-full md:w-auto"
            >
              Edit Profile
            </Button>
          )}

          {gState.accountProfileIsEdit.value && (
            <Button
              type="button"
              size="lg"
              onClick={() => {
                confirmDialog.set(true);
              }}
              className="w-full md:w-auto"
            >
              Save Information
            </Button>
          )}
        </div>
      )}

      {/* {isAdmin && profileTab.value === 'landbank_reqts' && (
        <div className="grid place-items-end">
          <LandbankDownloadButton />
        </div>
      )} */}

      {/* Tabs */}
      <ScrollArea className="">
        <div className="flex w-max items-center gap-8 pr-6">
          {PROFILE_TAB.map((tab, index) => {
            const isSelected = tab.value === profileTab;

            return (
              <button
                key={tab.value}
                className={cn(
                  'transition-all duration-300 ease-in-out whitespace-nowrap my-3',
                  isSelected
                    ? 'font-bold text-primary/90 underline underline-offset-8'
                    : 'text-gray-500 hover:font-bold hover:text-primary/80',
                )}
                onClick={() => {
                  if (dirty.value && isAdmin) {
                    cached.set({
                      profileTab: tab.value,
                      activeStep: index,
                    });

                    dirtyDialog.set(true);
                    return;
                  }

                  setProfileTab(tab.value);
                }}
              >
                {tab.name}
              </button>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      {/* Content */}
      <div className={cn(profileTab === 'loan_requirements' ? '' : 'rounded-lg border-2 border-dashed bg-white p-6')}>
        {profileTab === 'loan_requirements' && <LoanRequirements />}
        {profileTab === 'basic_info' && <BasicProfile />}
        {profileTab === 'career_academic' && <CareerAcademic />}
        {profileTab === 'identification_docs' && <IdentificationDocs />}
        {profileTab === 'family_profile' && <FamilyProfile />}
        {profileTab === 'property_ownership' && <PropertyOwnership />}
        {profileTab === 'farm_details' && <FarmDetails />}
        {profileTab === 'crop_insurance' && <FarmInsurance />}
        {/* {profileTab === 'landbank_reqts' && <LandbankReqts />} */}
      </div>

      <SaveConfirmation state={confirmDialog} activeStep={activeStep} />
      {isAdmin && gState.accountProfileIsEdit.value && (
        <DirtyDialog
          state={dirtyDialog}
          activeStep={activeStep}
          onDiscard={() => {
            dirty.set(false);
            setProfileTab(cached.profileTab.get({ noproxy: true }));
          }}
        />
      )}
    </div>
  );
}
